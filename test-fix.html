<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫雷游戏修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1e293b;
            color: #f1f5f9;
        }
        .test-section {
            background: #334155;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #475569;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #10b981;
            color: white;
        }
        .status.error {
            background: #ef4444;
            color: white;
        }
        .status.info {
            background: #0ea5e9;
            color: white;
        }
        code {
            background: #475569;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <h1>🔧 扫雷游戏修复验证工具</h1>
    
    <div class="test-section">
        <h2>🎯 问题描述</h2>
        <p>在普通浏览器模式下，扫雷游戏可能出现点击地雷格子没有反应的问题，而无痕模式正常。</p>
        <p><strong>根本原因：</strong> JavaScript中的 <code>mouseButtons</code> 状态管理缺陷导致左键点击被错误忽略。</p>
    </div>

    <div class="test-section">
        <h2>🔍 验证步骤</h2>
        <ol>
            <li>在普通模式下打开扫雷游戏</li>
            <li>如果遇到点击无反应的问题，请尝试以下方法：</li>
        </ol>
        
        <h3>方法1：在游戏区域外点击</h3>
        <p>在游戏棋盘外的任意位置点击一下，这会触发全局事件监听器重置鼠标状态。</p>
        
        <h3>方法2：使用调试工具</h3>
        <button class="test-button" onclick="openGameWithDebug()">打开调试模式游戏</button>
        <p>在调试模式下，控制台会显示详细的鼠标事件信息。</p>
        
        <h3>方法3：手动重置状态</h3>
        <p>在浏览器控制台中输入以下命令：</p>
        <code>resetMouseState()</code>
        <p>或查看当前状态：</p>
        <code>debugMouseState()</code>
    </div>

    <div class="test-section">
        <h2>🛠️ 修复内容</h2>
        <ul>
            <li>✅ 在游戏初始化时强制重置鼠标状态</li>
            <li>✅ 改进 handleMouseUp 逻辑，简化状态依赖</li>
            <li>✅ 增强全局鼠标事件监听器</li>
            <li>✅ 添加窗口失焦时的状态重置</li>
            <li>✅ 提供调试和手动重置功能</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🎮 测试游戏</h2>
        <button class="test-button" onclick="openGame()">打开修复后的游戏</button>
        <button class="test-button" onclick="openGameWithDebug()">打开调试模式</button>
        <button class="test-button" onclick="openOriginal()">打开原版对比</button>
        <button class="test-button" onclick="openIncognito()">无痕模式对比</button>
    </div>

    <div id="status" class="status info">
        <strong>状态：</strong>等待测试...
    </div>

    <script>
        function openGame() {
            window.open('https://minesweeper-fixed.heartwopen.workers.dev/', '_blank');
            updateStatus('已打开修复后的扫雷游戏，请测试点击功能', 'info');
        }

        function openGameWithDebug() {
            window.open('https://minesweeper-fixed.heartwopen.workers.dev/?debug=1', '_blank');
            updateStatus('已打开调试模式，请查看浏览器控制台', 'info');
        }

        function openOriginal() {
            window.open('https://mines.heartwopen.workers.dev/', '_blank');
            updateStatus('已打开原版游戏进行对比，可能存在点击问题', 'info');
        }

        function openIncognito() {
            updateStatus('请手动打开无痕窗口进行对比测试', 'info');
            alert('请按 Ctrl+Shift+N (Chrome) 或 Ctrl+Shift+P (Firefox) 打开无痕窗口，然后访问游戏进行对比。');
        }

        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
        }

        // 检查是否在游戏页面
        if (window.location.pathname === '/') {
            updateStatus('检测到游戏页面，修复已生效！', 'success');
        }
    </script>
</body>
</html>
